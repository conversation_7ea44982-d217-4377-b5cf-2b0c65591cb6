ALTER TABLE "public"."notify_channel_history"
    ALTER COLUMN "payload" TYPE varchar(20000);

ALTER TABLE "public"."alert"
  ADD COLUMN "route_policy_id" int4 NOT NULL DEFAULT 0,
  ADD COLUMN "alert_receiver" json NOT NULL DEFAULT '{}',
  ADD COLUMN "alert_receiver_department_id" int4 NOT NULL DEFAULT 0,
  ADD COLUMN "remark" varchar(2000) NOT NULL DEFAULT '';

COMMENT ON COLUMN "public"."alert"."route_policy_id" IS '分派策略ID|张恕征|2024-06-18';

COMMENT ON COLUMN "public"."alert"."alert_receiver" IS '接收人|张恕征|2024-06-18';

COMMENT ON COLUMN "public"."alert"."alert_receiver_department_id" IS '接收部门ID|张恕征|2024-06-18';

COMMENT ON COLUMN "public"."alert"."remark" IS '告警备注|张恕征|2024-06-18';