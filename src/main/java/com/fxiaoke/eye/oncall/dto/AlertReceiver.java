package com.fxiaoke.eye.oncall.dto;

import com.fxiaoke.eye.oncall.entity.Alert;
import com.fxiaoke.eye.oncall.entity.NotifyChannel;
import java.util.HashSet;
import java.util.Set;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 告警接收人
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Data
public class AlertReceiver {

  private Alert alert;

  private Set<NotifyChannel> notifyChannels = new HashSet<>();

  // 服务负责人
  private Set<User> users = new HashSet<>();

  // 服务订阅人
  private Set<User> subscribeUsers = new HashSet<>();

  private Set<String> webhooks = new HashSet<>();

  // 群发消息
  private Set<String> qunSessionIds = new HashSet<>();

  private Set<NotifyChannel> blackList = new HashSet<>();

  public AlertReceiver() {
    
  }

  public AlertReceiver(Alert alert) {
    this.alert = alert;
  }

  /**
   * 获取主负责人
   * @return
   */
  public Set<User> getMainOwner() {
    Set<User> mainOwner = new HashSet<>();
    // 服务负责人第一个 / 服务订阅人第一个
    if (!CollectionUtils.isEmpty(users)) {
      users.stream().findFirst().ifPresent(mainOwner::add);
    } else {
      subscribeUsers.stream().findFirst().ifPresent(mainOwner::add);
    }
    // 没有负责人，添加默认负责人
    if (CollectionUtils.isEmpty(mainOwner)) {
      User defaultUser = new User();
      defaultUser.setQixinId(11296);
      defaultUser.setUserName("告警机器人");
      defaultUser.setNickName("告警机器人");
      defaultUser.setPhone("");
      defaultUser.setEmail("");
      mainOwner.add(defaultUser);
    }
    return mainOwner;
  }

  @Data
  public static class User {

    private Integer qixinId;

    private String userName;

    private String nickName;

    private String phone;

    private String email;

    private String webhook;
  }

  public Set<Integer> getUserQixinIds() {
    Set<Integer> qixinIds = new HashSet<>();
    Set<User> allUsers = new HashSet<>();
    allUsers.addAll(users);
    allUsers.addAll(subscribeUsers);
    allUsers.forEach(user -> {
      if (user.getQixinId() != null) {
        qixinIds.add(user.getQixinId());
      }
    });
    return qixinIds;
  }

  public Set<String> getUserUserNames() {
    Set<String> userNames = new HashSet<>();
    Set<User> allUsers = new HashSet<>();
    allUsers.addAll(users);
    allUsers.addAll(subscribeUsers);
    allUsers.forEach(user -> {
      if (user.getUserName() != null) {
        userNames.add(user.getUserName());
      }
    });
    return userNames;
  }

  public Set<String> getUserNickNames() {
    Set<String> nickNames = new HashSet<>();
    Set<User> allUsers = new HashSet<>();
    allUsers.addAll(users);
    allUsers.addAll(subscribeUsers);
    allUsers.forEach(user -> {
      if (user.getNickName() != null) {
        nickNames.add(user.getNickName());
      }
    });
    return nickNames;
  }

  public Set<String> getUserPhones() {
    Set<String> phones = new HashSet<>();
    Set<User> allUsers = new HashSet<>();
    allUsers.addAll(users);
    allUsers.addAll(subscribeUsers);
    allUsers.forEach(user -> {
      if (user.getPhone() != null) {
        phones.add(user.getPhone());
      }
    });
    return phones;
  }

  public Set<String> getUserEmails() {
    Set<String> emails = new HashSet<>();
    Set<User> allUsers = new HashSet<>();
    allUsers.addAll(users);
    allUsers.addAll(subscribeUsers);
    allUsers.forEach(user -> {
      if (user.getEmail() != null) {
        emails.add(user.getEmail());
      }
    });
    return emails;
  }

  public Set<String> getUserWebhooks() {
    Set<String> webhooks = new HashSet<>();
    Set<User> allUsers = new HashSet<>();
    allUsers.addAll(users);
    allUsers.addAll(subscribeUsers);
    allUsers.forEach(user -> {
      if (user.getWebhook() != null) {
        webhooks.add(user.getWebhook());
      }
    });
    return webhooks;
  }

  public boolean isSilence(String channelName) {
    boolean silence = false;
    for (NotifyChannel notifyChannel : this.getBlackList()) {
      if (notifyChannel.getPolicyName().equalsIgnoreCase(channelName)) {
        silence = true;
      }
    }
    return silence;
  }

}
