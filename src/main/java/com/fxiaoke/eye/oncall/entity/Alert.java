package com.fxiaoke.eye.oncall.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fxiaoke.eye.oncall.enums.AlertLevelEnum;
import com.fxiaoke.eye.oncall.enums.AlertProcessEnum;
import com.fxiaoke.eye.oncall.enums.AlertSourceEnum;
import com.fxiaoke.eye.oncall.enums.AlertStatusEnum;
import com.fxiaoke.eye.oncall.handler.MybatisJsonTypeHandler;
import java.util.Date;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警表|张恕征|2023-11-10
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "alert", autoResultMap = true)
public class Alert {

    /**
     * 主键|张恕征|2023-11-10
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 告警UID|张恕征|2023-11-10
     */
    private String uid;

    /**
     * 告警来源|张恕征|2023-11-10
     */
    private AlertSourceEnum alertSource;

    /**
     * 告警环境|张恕征|2023-11-10
     */
    private String alertEnv;

    /**
     * 告警请求ID|张恕征|2023-11-10
     */
    private Integer alertRequestId;

    /**
     * 告警指纹|张恕征|2023-11-10
     */
    private String fingerprint;

    /**
     * 告警名称|张恕征|2023-11-10
     */
    private String alertName;

    /**
     * 告警标题|张恕征|2023-11-10
     */
    private String alertTitle;

    /**
     * 告警等级:0严重,1警告,2通知|张恕征|2023-11-10
     */
    private AlertLevelEnum alertLevel;

    /**
     * 告警等级:0恢复,1告警|张恕征|2023-11-10
     */
    private AlertStatusEnum alertStatus;

    /**
     * 告警资源类型|张恕征|2023-11-10
     */
    private String resourceType;

    /**
     * 告警资源实例|张恕征|2023-11-10
     */
    private String resourceId;

    /**
     * 流程状态:0告警,1跟进,2忽略,3静默,4自动恢复,5手动恢复|张恕征|2023-11-10
     */
    private AlertProcessEnum processStatus;

    /**
     * 告警次数|张恕征|2023-11-10
     */
    private Integer alertCount;

    /**
     * 告警内容|张恕征|2023-11-10
     */
    private String alertContent;

    /**
     * 告警开始时间|张恕征|2023-11-10
     */
    private Date startTime;

    /**
     * 告警结束时间|张恕征|2023-11-10
     */
    private Date endTime;

    /**
     * 升级级别|张恕征|2023-11-10
     */
    private Integer upgradeLevel;

    /**
     * 告警标签|张恕征|2023-11-10
     */
    @TableField(typeHandler = MybatisJsonTypeHandler.class)
    private JSONObject alertLabel;

    /**
     * 告警源数据|张恕征|2023-11-10
     */
    @TableField(typeHandler = MybatisJsonTypeHandler.class)
    private JSONObject alertJson;

    /**
     * 版本|张恕征|2023-11-10
     */
    @Version
    private Integer version;

    /**
     * 删除|张恕征|2023-11-10
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间|张恕征|2023-11-10
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间|张恕征|2023-11-10
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private String description;

    @TableField(exist = false)
    private String runbookUrl;

    private String namespace;

    private String resourceName;

    @TableField(exist = false)
    private Map<String, String> annotations;

    /**
     * 分派策略ID|张恕征|2024-06-18
     */
    private Integer routePolicyId;

    /**
     * 接收人|张恕征|2024-06-18
     */
    @TableField(typeHandler = MybatisJsonTypeHandler.class)
    private JSONObject alertReceiver;

    /**
     * 接收部门ID|张恕征|2024-06-18
     */
    private Integer alertReceiverDepartmentId;

    /**
     * 告警备注|张恕征|2024-06-18
     */
    private String remark;

}
